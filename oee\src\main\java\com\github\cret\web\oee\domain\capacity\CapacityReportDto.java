package com.github.cret.web.oee.domain.capacity;

import java.util.List;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 产能报表数据传输对象
 */
@ExcelIgnoreUnannotated
public class CapacityReportDto {

	@ExcelProperty("开始时间")
	private String startWorkTime;

	@ExcelProperty("结束时间")
	private String endWorkTime;

	// 实际产能
	@ExcelProperty("实际产能")
	private int actualQuantity;

	@ExcelProperty("理论产能")
	private int theoreticalQuantity;

	// 达成率
	@ExcelProperty("达成率")
	private String achievementRate;

	// 达成状态
	@ExcelProperty("达成状态")
	private String status;

	// 其他原因
	@ExcelProperty("其他原因")
	private String reason;

	// 间隔时间
	private int interval;

	private List<CapacityProductInfo> capacityProductInfos;

	private Double stopTime;

	public String getStartWorkTime() {
		return startWorkTime;
	}

	public void setStartWorkTime(String startWorkTime) {
		this.startWorkTime = startWorkTime;
	}

	public String getEndWorkTime() {
		return endWorkTime;
	}

	public void setEndWorkTime(String endWorkTime) {
		this.endWorkTime = endWorkTime;
	}

	public int getActualQuantity() {
		return actualQuantity;
	}

	public void setActualQuantity(int actualQuantity) {
		this.actualQuantity = actualQuantity;
	}

	public int getTheoreticalQuantity() {
		return theoreticalQuantity;
	}

	public void setTheoreticalQuantity(int theoreticalQuantity) {
		this.theoreticalQuantity = theoreticalQuantity;
	}

	public String getAchievementRate() {
		return achievementRate;
	}

	public void setAchievementRate(String achievementRate) {
		this.achievementRate = achievementRate;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getReason() {
		return reason;
	}

	public void setReason(String reason) {
		this.reason = reason;
	}

	public int getInterval() {
		return interval;
	}

	public void setInterval(int interval) {
		this.interval = interval;
	}

	public List<CapacityProductInfo> getCapacityProductInfos() {
		return capacityProductInfos;
	}

	public void setCapacityProductInfos(List<CapacityProductInfo> capacityProductInfos) {
		this.capacityProductInfos = capacityProductInfos;
	}

	public Double getStopTime() {
		return stopTime;
	}

	public void setStopTime(Double stopTime) {
		this.stopTime = stopTime;
	}

}
